



import { notFound } from 'next/navigation';
import Link from 'next/link';
import { toolCategories, Tool } from '@/lib/tools';
import { PageHeader } from '@/components/PageHeader';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { loadToolContent } from '@/lib/content/loader';
import type { Metadata, ResolvingMetadata } from 'next';
import { JsonLd } from '@/components/JsonLd';
import type { BreadcrumbList, FAQPage } from 'schema-dts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft } from 'lucide-react';
import { cn } from '@/lib/utils';

const siteUrl = process.env.NEXT_PUBLIC_SITE_URL;

type Props = {
  params: { slug: string };
};

export async function generateMetadata(
  { params }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const { slug } = params;
  const tool = toolCategories.flatMap(c => c.tools).find(t => t.slug === slug);

  if (!tool) {
    return {
      title: 'أداة غير موجودة',
    };
  }

  const content = await loadToolContent(slug);
  const description = content.seoDescription?.replace(/<[^>]*>?/gm, '').substring(0, 160) || tool.description;
  const previousImages = (await parent).openGraph?.images || [];
  const toolUrl = siteUrl ? `${siteUrl}/tools/${slug}` : `/tools/${slug}`;

  return {
    title: tool.name,
    description: description,
    alternates: {
      canonical: `/tools/${slug}`,
    },
    openGraph: {
      title: `${tool.name} | أدوات بالعربي`,
      description: description,
      url: toolUrl,
      images: [...previousImages],
      type: 'article',
    },
    twitter: {
      title: `${tool.name} | أدوات بالعربي`,
      description: description,
      images: [...previousImages],
    },
  };
}

export default async function ToolPage({ params }: { params: { slug: string } }) {
  const { slug } = params;
  const allTools = toolCategories.flatMap(c => c.tools);
  const tool = allTools.find(t => t.slug === slug);
  const category = toolCategories.find(cat => cat.tools.some(t => t.slug === slug));

  if (!tool || !tool.component) {
    notFound();
  }

  const content = await loadToolContent(tool.slug);
  const enrichedTool = { ...tool, ...content };

  let toolProps = {};
  if (tool.getData) {
    const data = await tool.getData();
    toolProps = { initialData: data };
  }

  // --- Improved Related Tools Logic ---
  let relatedTools: Tool[] = [];
  const relatedSlugs = new Set<string>();

  // 1. Add manually specified related tools first
  if (enrichedTool.relatedSlugs) {
    enrichedTool.relatedSlugs.forEach(relatedSlug => {
      const relatedTool = allTools.find(t => t.slug === relatedSlug);
      if (relatedTool && relatedTool.component && !relatedSlugs.has(relatedSlug)) {
        relatedTools.push(relatedTool);
        relatedSlugs.add(relatedSlug);
      }
    });
  }

  // 2. Add other tools from the same category to fill up the list
  if (category && relatedTools.length < 6) { // Limit total suggestions
    const categoryTools = category.tools.filter(t => t.slug !== slug && t.component && !relatedSlugs.has(t.slug));
    const remainingTools = categoryTools.slice(0, 6 - relatedTools.length);
    relatedTools.push(...remainingTools);
  }
  // --- End of Improved Logic ---
  
  const ToolComponent = tool.component;

  const breadcrumbJsonLd: BreadcrumbList | null = siteUrl && category ? {
    '@type': 'BreadcrumbList',
    itemListElement: [
      { '@type': 'ListItem', position: 1, name: 'الرئيسية', item: siteUrl },
      { '@type': 'ListItem', position: 2, name: category.name, item: `${siteUrl}/categories/${category.slug}` },
      { '@type': 'ListItem', position: 3, name: tool.name, item: `${siteUrl}/tools/${slug}` },
    ],
  } : null;

  const faqJsonLd: FAQPage | null = enrichedTool.faq && enrichedTool.faq.length > 0 ? {
    '@type': 'FAQPage',
    mainEntity: enrichedTool.faq.map(item => ({
      '@type': 'Question',
      name: item.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: item.answer,
      },
    })),
  } : null;

  const isWideTool = ['summarize-arabic-text', 'paraphrase-text'].includes(slug);

  return (
    <div className="w-full flex flex-col items-center">
      {breadcrumbJsonLd && <JsonLd data={breadcrumbJsonLd} />}
      {faqJsonLd && <JsonLd data={faqJsonLd} />}
      
      <div className={cn(
          "w-full flex justify-center",
          isWideTool ? "max-w-6xl" : "max-w-4xl"
      )}>
        <div className={cn(
          "w-full",
           isWideTool ? "max-w-6xl" : "max-w-2xl"
        )}>
            <ToolComponent {...toolProps} />
        </div>
      </div>
      
       <div className="w-full max-w-4xl mt-16 space-y-12">
            {relatedTools.length > 0 && (
              <section>
                <h2 className="text-2xl font-headline font-bold text-center mb-6">أدوات ذات صلة</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {relatedTools.map(relatedTool => (
                    <Link key={relatedTool.path} href={relatedTool.path} className="group">
                      <Card className="h-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-primary">
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-4">
                                    {relatedTool.icon && (
                                        <div className="p-2 rounded-full bg-primary/10 text-primary">
                                            <relatedTool.icon className="w-5 h-5" />
                                        </div>
                                    )}
                                    <CardTitle className="font-headline text-lg">{relatedTool.name}</CardTitle>
                                </div>
                                <ArrowLeft className="h-5 w-5 text-muted-foreground transition-transform group-hover:translate-x-[-4px] group-hover:text-primary shrink-0" />
                            </div>
                        </CardHeader>
                         <CardContent>
                            <p className="text-sm text-muted-foreground">
                              {relatedTool.description}
                            </p>
                          </CardContent>
                      </Card>
                    </Link>
                  ))}
                </div>
              </section>
            )}

            {enrichedTool.seoDescription && (
              <article>
                 <h2 className="text-2xl font-headline font-bold text-center mb-6">حول أداة {enrichedTool.name}</h2>
                 <div className="p-6 bg-card border rounded-lg space-y-4 text-base leading-relaxed text-card-foreground/90 prose-p:my-4 prose-ul:my-4 prose-ol:my-4" dangerouslySetInnerHTML={{ __html: enrichedTool.seoDescription }} />
              </article>
            )}
      
            {enrichedTool.faq && enrichedTool.faq.length > 0 && (
              <section>
                <h2 className="text-2xl font-headline font-bold text-center mb-6">أسئلة شائعة</h2>
                <Accordion type="single" collapsible className="w-full">
                  {enrichedTool.faq.map((item, index) => (
                    <AccordionItem value={`item-${index}`} key={index}>
                      <AccordionTrigger className="text-right text-lg">{item.question}</AccordionTrigger>
                      <AccordionContent className="text-base leading-relaxed">
                        <p>{item.answer}</p>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </section>
            )}
        </div>
    </div>
  );
}

export async function generateStaticParams() {
  const paths = toolCategories
    .flatMap(category => category.tools)
    .filter(tool => tool.component)
    .map(tool => ({
      slug: tool.slug,
    }));

  return paths;
}
